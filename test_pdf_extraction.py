#!/usr/bin/env python3
"""
Test script to verify PDF extraction functionality with the sample PDF
"""

import requests
import os
import json
import sys
import time

def test_direct_extraction():
    """Test the extraction directly using the extract_tariff module"""
    print("🔧 Testing direct extraction...")

    # Add Backend to path
    sys.path.insert(0, 'Backend')

    try:
        from extract_tariff import extract_tariff_from_pdf

        pdf_path = "Hotel Pahalagm-June-2025.pdf"
        if not os.path.exists(pdf_path):
            print(f"❌ Sample PDF not found: {pdf_path}")
            return False

        print(f"📄 Testing direct extraction with: {pdf_path}")

        # Test with LLM
        print("🤖 Testing with LLM extraction...")
        result = extract_tariff_from_pdf(pdf_path, use_llm=True)

        print(f"📊 Direct extraction result: {len(result) if result else 0} records")

        if result:
            print("✅ Direct extraction successful!")
            print("📋 Sample records:")
            for i, record in enumerate(result[:2]):
                print(f"  Record {i+1}: {record}")
            return True
        else:
            print("❌ Direct extraction returned no data")

            # Test without LLM as fallback
            print("🔄 Testing without LLM extraction...")
            result_no_llm = extract_tariff_from_pdf(pdf_path, use_llm=False)
            print(f"📊 Fallback extraction result: {len(result_no_llm) if result_no_llm else 0} records")

            if result_no_llm:
                print("✅ Fallback extraction successful!")
                return True

            return False

    except Exception as e:
        print(f"❌ Direct extraction failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_pdf_extraction():
    """Test the PDF extraction API with the sample PDF"""

    # API endpoint
    url = "http://localhost:5000/api/extract-tariff"

    # Path to the sample PDF
    pdf_path = "Hotel Pahalagm-June-2025.pdf"

    if not os.path.exists(pdf_path):
        print(f"❌ Sample PDF not found: {pdf_path}")
        return False

    print(f"📄 Testing PDF extraction with: {pdf_path}")
    print(f"📊 File size: {os.path.getsize(pdf_path)} bytes")

    try:
        # Prepare the file for upload
        with open(pdf_path, 'rb') as pdf_file:
            files = {
                'file': (pdf_path, pdf_file, 'application/pdf')
            }
            data = {
                'use_llm': 'true'  # Test with LLM extraction
            }

            print("🚀 Sending request to extraction API...")
            response = requests.post(url, files=files, data=data, timeout=120)

        print(f"📡 Response status: {response.status_code}")

        if response.status_code == 200:
            result = response.json()
            print("✅ Extraction successful!")
            print(f"📈 Success: {result.get('success', False)}")
            print(f"📊 Records extracted: {result.get('count', 0)}")

            if result.get('data'):
                print("\n📋 Sample extracted data:")
                for i, record in enumerate(result['data'][:3]):  # Show first 3 records
                    print(f"  Record {i+1}:")
                    for key, value in record.items():
                        print(f"    {key}: {value}")
                    print()

                if len(result['data']) > 3:
                    print(f"    ... and {len(result['data']) - 3} more records")

            return True
        else:
            print(f"❌ Extraction failed with status {response.status_code}")
            try:
                error_data = response.json()
                print(f"Error: {error_data.get('error', 'Unknown error')}")
            except:
                print(f"Response text: {response.text}")
            return False

    except requests.exceptions.RequestException as e:
        print(f"❌ Request failed: {e}")
        return False
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        return False

def test_health_check():
    """Test if the API is running"""
    try:
        response = requests.get("http://localhost:5000/api/health", timeout=5)
        if response.status_code == 200:
            print("✅ API health check passed")
            return True
        else:
            print(f"❌ API health check failed: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ API health check failed: {e}")
        return False

if __name__ == "__main__":
    print("🔍 Testing PDF Extraction Functionality")
    print("=" * 50)

    # Test API health first
    if not test_health_check():
        print("❌ API is not running. Please start the backend server first.")
        exit(1)

    # Test direct extraction first to debug
    print("\n🔧 Step 1: Testing direct extraction...")
    direct_success = test_direct_extraction()

    # Test API extraction
    print("\n🌐 Step 2: Testing API extraction...")
    api_success = test_pdf_extraction()

    print("\n" + "=" * 50)
    print("📊 Test Results:")
    print(f"  Direct extraction: {'✅ Success' if direct_success else '❌ Failed'}")
    print(f"  API extraction: {'✅ Success' if api_success else '❌ Failed'}")

    if direct_success or api_success:
        print("✅ At least one extraction method worked!")
    else:
        print("❌ Both extraction methods failed!")

    print("🔍 Check the Backend/output/ directory for detailed extraction results.")
