#!/usr/bin/env python3
"""
Test script to check OCR functionality directly via HTTP API
"""

import requests
import base64
import os
import json
from dotenv import load_dotenv

# Load environment variables
load_dotenv('Backend/.env')
api_key = os.getenv("MISTRAL_API_KEY")

def test_ocr_api_direct():
    """Test OCR API directly via HTTP requests"""
    print("🔍 Testing Mistral OCR API directly...")
    
    if not api_key:
        print("❌ No API key found")
        return False
    
    # Path to the sample PDF
    pdf_path = "Hotel Pahalagm-June-2025.pdf"
    
    if not os.path.exists(pdf_path):
        print(f"❌ Sample PDF not found: {pdf_path}")
        return False
    
    try:
        # Encode PDF to base64
        with open(pdf_path, "rb") as f:
            base64_pdf = base64.b64encode(f.read()).decode("utf-8")
        
        print(f"📄 PDF encoded, size: {len(base64_pdf)} characters")
        
        # Prepare the API request
        url = "https://api.mistral.ai/v1/ocr"
        headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {api_key}"
        }
        
        data = {
            "model": "mistral-ocr-latest",
            "document": {
                "type": "document_url",
                "document_url": f"data:application/pdf;base64,{base64_pdf}"
            },
            "include_image_base64": False
        }
        
        print("🚀 Sending OCR request...")
        response = requests.post(url, headers=headers, json=data, timeout=120)
        
        print(f"📡 Response status: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print("✅ OCR successful!")
            
            # Check if we have pages with markdown content
            if 'pages' in result:
                print(f"📊 Pages extracted: {len(result['pages'])}")
                
                # Extract text from all pages
                full_text = ""
                for i, page in enumerate(result['pages']):
                    if 'markdown' in page:
                        page_text = page['markdown']
                        full_text += page_text + "\n"
                        print(f"  Page {i+1}: {len(page_text)} characters")
                
                print(f"📝 Total text extracted: {len(full_text)} characters")
                
                if full_text:
                    # Save the extracted text
                    os.makedirs('Backend/output', exist_ok=True)
                    with open('Backend/output/ocr_response.txt', 'w', encoding='utf-8') as f:
                        f.write(full_text)
                    print("💾 Saved OCR text to Backend/output/ocr_response.txt")
                    
                    # Show a preview
                    print("\n📋 Text preview (first 500 chars):")
                    print(full_text[:500])
                    print("...")
                    
                    return True
                else:
                    print("❌ No text content found in pages")
                    return False
            else:
                print("❌ No pages found in response")
                print(f"Response keys: {list(result.keys())}")
                return False
                
        else:
            print(f"❌ OCR failed with status {response.status_code}")
            try:
                error_data = response.json()
                print(f"Error: {error_data}")
            except:
                print(f"Response text: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🔍 Testing Mistral OCR API Direct")
    print("=" * 50)
    
    success = test_ocr_api_direct()
    
    print("\n" + "=" * 50)
    if success:
        print("✅ OCR API test completed successfully!")
        print("🔧 The issue is likely with the Python client library version.")
        print("💡 Consider updating the extract_tariff.py to use direct HTTP calls.")
    else:
        print("❌ OCR API test failed!")
        print("🔍 Check API key and network connectivity.")
