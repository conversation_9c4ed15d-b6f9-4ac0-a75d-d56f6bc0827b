#!/usr/bin/env python3
"""
Test script to check Mistral API capabilities
"""

import os
import sys
sys.path.insert(0, 'Backend')

from mistralai import Mistral
from dotenv import load_dotenv

# Load environment variables
load_dotenv('Backend/.env')
api_key = os.getenv("MISTRAL_API_KEY")

def test_mistral_client():
    """Test what's available in the Mistral client"""
    print("🔍 Testing Mistral API client...")
    print(f"API Key present: {'Yes' if api_key else 'No'}")
    
    if not api_key:
        print("❌ No API key found")
        return False
    
    try:
        client = Mistral(api_key=api_key)
        print("✅ Mistral client created successfully")
        
        # Check available attributes
        print("\n📋 Available client attributes:")
        for attr in dir(client):
            if not attr.startswith('_'):
                print(f"  - {attr}")
        
        # Check if OCR is available
        if hasattr(client, 'ocr'):
            print("✅ OCR attribute found")
        else:
            print("❌ OCR attribute not found")
            
        # Check if there's a different way to access OCR
        if hasattr(client, 'vision'):
            print("✅ Vision attribute found")
        elif hasattr(client, 'multimodal'):
            print("✅ Multimodal attribute found")
        else:
            print("❌ No vision/multimodal attributes found")
            
        # Try to list available models
        try:
            models = client.models.list()
            print(f"\n📊 Available models: {len(models.data) if hasattr(models, 'data') else 'Unknown'}")
            if hasattr(models, 'data'):
                for model in models.data[:5]:  # Show first 5 models
                    print(f"  - {model.id}")
                if len(models.data) > 5:
                    print(f"  ... and {len(models.data) - 5} more")
        except Exception as e:
            print(f"❌ Error listing models: {e}")
            
        return True
        
    except Exception as e:
        print(f"❌ Error creating Mistral client: {e}")
        return False

if __name__ == "__main__":
    test_mistral_client()
